// 缓存工具函数

/**
 * 设置 localStorage
 * @param key 键名
 * @param value 值
 */
export const setLocalStorage = (key: string, value: any): void => {
  try {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value)
    localStorage.setItem(key, stringValue)
  } catch (error) {
    console.error('设置 localStorage 失败:', error)
  }
}

/**
 * 获取 localStorage
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的值或默认值
 */
export const getLocalStorage = (key: string, defaultValue: any = null): any => {
  try {
    const value = localStorage.getItem(key)
    if (value === null) return defaultValue
    
    // 尝试解析 JSON，如果失败则返回原始字符串
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  } catch (error) {
    console.error('获取 localStorage 失败:', error)
    return defaultValue
  }
}

/**
 * 删除 localStorage
 * @param key 键名
 */
export const removeLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('删除 localStorage 失败:', error)
  }
}

/**
 * 清空 localStorage
 */
export const clearLocalStorage = (): void => {
  try {
    localStorage.clear()
  } catch (error) {
    console.error('清空 localStorage 失败:', error)
  }
}

/**
 * 设置 sessionStorage
 * @param key 键名
 * @param value 值
 */
export const setSessionStorage = (key: string, value: any): void => {
  try {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value)
    sessionStorage.setItem(key, stringValue)
  } catch (error) {
    console.error('设置 sessionStorage 失败:', error)
  }
}

/**
 * 获取 sessionStorage
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的值或默认值
 */
export const getSessionStorage = (key: string, defaultValue: any = null): any => {
  try {
    const value = sessionStorage.getItem(key)
    if (value === null) return defaultValue
    
    // 尝试解析 JSON，如果失败则返回原始字符串
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  } catch (error) {
    console.error('获取 sessionStorage 失败:', error)
    return defaultValue
  }
}

/**
 * 删除 sessionStorage
 * @param key 键名
 */
export const removeSessionStorage = (key: string): void => {
  try {
    sessionStorage.removeItem(key)
  } catch (error) {
    console.error('删除 sessionStorage 失败:', error)
  }
}

/**
 * 清空 sessionStorage
 */
export const clearSessionStorage = (): void => {
  try {
    sessionStorage.clear()
  } catch (error) {
    console.error('清空 sessionStorage 失败:', error)
  }
}

// 导出默认对象
export default {
  setLocalStorage,
  getLocalStorage,
  removeLocalStorage,
  clearLocalStorage,
  setSessionStorage,
  getSessionStorage,
  removeSessionStorage,
  clearSessionStorage,
}
