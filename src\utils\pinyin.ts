// 拼音搜索工具函数
import { pinyin } from 'pinyin-pro'

/**
 * 支持拼音搜索的过滤函数
 * @param input 搜索输入
 * @param option 选项对象
 * @param fieldName 要搜索的字段名
 * @returns 是否匹配
 */
export const filterOptionWithPinyin = (input: string, option: any, fieldName: string = 'label'): boolean => {
  if (!input) return true
  if (!option || !option[fieldName]) return false
  
  const searchText = input.toLowerCase()
  const targetText = option[fieldName]
  
  // 1. 直接文本匹配
  if (targetText.toLowerCase().includes(searchText)) {
    return true
  }
  
  try {
    // 2. 全拼匹配
    const fullPinyin = pinyin(targetText, { toneType: 'none' })
      .replace(/\s+/g, '')
      .toLowerCase()
    if (fullPinyin.includes(searchText)) {
      return true
    }
    
    // 3. 首字母匹配
    const firstPinyin = pinyin(targetText, { toneType: 'none' })
      .split(/\s+/)
      .map((word) => word.charAt(0))
      .join('')
      .toLowerCase()
    if (firstPinyin.includes(searchText)) {
      return true
    }
  } catch (error) {
    console.warn('拼音转换失败:', error)
    // 如果拼音转换失败，回退到普通文本搜索
    return targetText.toLowerCase().includes(searchText)
  }
  
  return false
}

/**
 * 数组过滤函数，支持拼音搜索
 * @param input 搜索输入
 * @param options 选项数组
 * @param fieldName 要搜索的字段名
 * @returns 过滤后的数组
 */
export const filterArrayWithPinyin = (input: string, options: any[], fieldName: string = 'label'): any[] => {
  if (!input) return options
  
  return options.filter(option => filterOptionWithPinyin(input, option, fieldName))
}

// 导出默认函数
export default filterOptionWithPinyin
