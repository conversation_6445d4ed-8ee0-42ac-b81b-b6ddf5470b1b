import { pageTableConfig } from '@/common/pageTableConfig'
import { GetTableConfig, SetTableConfig } from '@/servers/Common'
import eventBus from '@/utils/eventBus'
import useCommonOptionStore from '@/store/modules/commonOption'

const lineHeightType = 2 // 行高类型
/**
 * 数字格式化函数
 *
 * 该函数提供了一种灵活的方式将数字格式化为字符串，包括设置精度、千位分隔符、小数点字符、前缀和后缀
 *
 * @param value 要格式化的数字或数字字符串
 * @param precision 小数点后的位数，默认为 2
 * @param separator 千分位分隔符，默认为 ','
 * @param decimal 小数点字符，默认为 '.'
 * @param prefix 数字前的字符串，默认为 undefined
 * @param suffix 数字后的字符串，默认为 undefined
 * @returns 格式化后的字符串；如果输入值不是数字或字符串，则抛出类型错误
 */
export function formatNumber(value: number | string, precision: number = 2, separator: string = ',', decimal: string = '.', prefix?: string, suffix?: string): string {
  // 类型检查
  if (typeof value !== 'number' && typeof value !== 'string') {
    console.warn('Expected value to be of type number or string')
  }
  if (typeof precision !== 'number') {
    console.warn('Expected precision to be of type number')
  }
  // 处理非数值或NaN的情况
  const numValue = Number(value)
  if (Number.isNaN(numValue) || !Number.isFinite(numValue)) {
    return ''
  }
  if (numValue === 0) {
    return numValue.toFixed(precision)
  }
  let formatValue = numValue.toFixed(precision)
  // 如果 separator 是数值而非字符串，会导致错误，此处进行检查
  if (typeof separator === 'string' && separator !== '') {
    const [integerPart, decimalPart] = formatValue.split('.')
    formatValue = integerPart.replace(/(\d)(?=(\d{3})+$)/g, `$1${separator}`) + (decimalPart ? decimal + decimalPart : '')
  }
  return (prefix || '') + formatValue + (suffix || '')
}

// 节流函数
export const throttle = (func, wait) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  let lastArgs: any[] | null = null

  const throttled = (...args) => {
    if (!timeout) {
      func(...args)
      timeout = setTimeout(() => {
        timeout = null
        if (lastArgs) {
          func(...lastArgs)
          lastArgs = null
        }
      }, wait)
    } else {
      lastArgs = args
    }
  }

  return throttled
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return (...args) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 定义泛型类型，支持带参数的函数

// eslint-disable-next-line no-unused-vars
type DebouncedFunction<T extends (...args: any[]) => any> = (...args: Parameters<T>) => ReturnType<T> | void

/**
 * 按钮防抖函数
 * @param func 需要防抖的函数
 * @param wait 防抖等待时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 防抖处理后的函数
 */

// eslint-disable-next-line no-unused-vars
export const buttonDebounce = <T extends (...args: any[]) => any>(func: T, wait: number = 500, immediate: boolean = true): DebouncedFunction<T> => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  let isExecuting = false

  return function (this: ThisParameterType<T>, ...args: Parameters<T>): ReturnType<T> | void {
    const callNow = immediate && !timeout && !isExecuting

    const later = () => {
      timeout = null
      isExecuting = false
      if (!immediate) {
        func.apply(this, args)
      }
    }

    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(later, wait)

    if (callNow && !isExecuting) {
      isExecuting = true
      const result = func.apply(this, args)

      // 如果返回Promise，等待完成后重置状态
      if (result && typeof result.then === 'function') {
        result.finally(() => {
          isExecuting = false
        })
      } else {
        // 非Promise情况下，延迟重置状态
        setTimeout(() => {
          isExecuting = false
        }, 100)
      }

      return result
    }
  }
}

/**
 * 创建带loading状态的防抖函数
 * @param func 要执行的异步函数
 * @param loadingRef loading状态的ref
 * @param wait 防抖等待时间，默认500ms
 * @returns 防抖后的函数
 */
export const createLoadingDebounce = <T extends () => Promise<any>>(func: T, loadingRef: { value: boolean }, wait: number = 500) => {
  let isExecuting = false

  return async function (this: ThisParameterType<T>, ...args: Parameters<T>): Promise<ReturnType<T>> {
    // 如果正在执行或已加载中，则直接返回
    if (isExecuting || loadingRef.value) {
      return Promise.resolve() as ReturnType<T>
    }

    isExecuting = true
    loadingRef.value = true

    try {
      // 执行原始函数并保留返回值
      const result = await func.apply(this, args)
      return result
    } finally {
      // 延迟重置状态，确保loading效果可见
      setTimeout(() => {
        loadingRef.value = false
        isExecuting = false
      }, wait)
    }
  }
}

/**
 * @description 设缓存
 */
export const setLocal = (key, value) => {
  if (typeof value === 'object') value = JSON.stringify(value)

  sessionStorage.setItem(key, value)
}

/**
 * @description 获取缓存
 */
export const getLocal = (key = '') => {
  const value = sessionStorage.getItem(key) || ''
  try {
    return JSON.parse(value)
  } catch (err) {
    console.error(err)
    return value
  }
}

/**
 * @description 清除缓存，如有指定key则删除单个，否则删除全部
 */
export function clearLocal(key) {
  if (key) {
    sessionStorage.removeItem(key)
  } else {
    sessionStorage.clear()
  }
}
/**
 * @description: 生成随机数
 * @return {*}
 */
export const getUnid = () => {
  return Math.random().toString(36).substr(2, 10)
}

/**
 * @description 获取浏览器类型
 */
export const getBrowser = () => {
  const browser = {
    versions: (() => {
      const u = navigator.userAgent
      const platform = navigator.platform
      return {
        trident: u.indexOf('Trident') > -1, // IE内核
        presto: u.indexOf('Presto') > -1, // opera内核
        webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
        gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') === -1, // 火狐内核
        mobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
        ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios终端
        android: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, // android终端
        iPhone: u.indexOf('iPhone') > -1, // 是否为iPhone或者QQHD浏览器
        iPad: u.indexOf('iPad') > -1, // 是否iPad
        webApp: u.indexOf('Safari') === -1, // 是否web应该程序，没有头部与底部
        weixin: u.indexOf('MicroMessenger') > -1, // 是否微信 （2015-01-22新增）
        qq: u.match(/\sQQ/i), // 是否QQ
        win: platform.indexOf('Win') > -1,
      }
    })(),
    language: navigator.language.toLowerCase(),
  }
  return browser
}

export const upLoadXlsx = (res, fileName = 'table') => {
  const { data, headers } = res
  const blob = new Blob([data], { type: `${headers['content-type']};charset=utf-8` })
  const dom = document.createElement('a')
  const url = window.URL.createObjectURL(blob)
  dom.href = url
  dom.download = decodeURI(`${fileName}.xlsx`)
  dom.style.display = 'none'
  document.body.appendChild(dom)
  dom.click()
  // dom.parentNode && dom.parentNode.removeChild(dom)
  if (dom.parentNode) {
    dom.parentNode.removeChild(dom)
  }
  window.URL.revokeObjectURL(url)
}

// form表单请求参数校验
export const checkFormParams = (data) => {
  const { formArr, obj = {}, callBack } = data
  formArr.forEach((item) => {
    if (item.formKeys && item.formKeys.length && item.value) {
      item.formKeys.forEach((v, i) => {
        obj[v] = item.value[i] || null
      })
    } else obj[item.key] = item.value

    // if (item.multiple) {
    //   obj[item.key] = Array.isArray(obj[item.key]) ? item.value.join(',') : item.value
    // }
  })
  for (const key in obj) {
    if ((!obj[key] && obj[key] != 0) || obj[key].length < 1) {
      obj[key] = null
    }
  }
  if (callBack) {
    callBack(obj)
  }
  return obj
}
// 保存列表配置
export const setTableConfig = (arr, page_type, line_height) => {
  return new Promise((resolve) => {
    const data = cloneDeep({ maps: arr, page_type, line_height })
    data.maps.sort((a, b) => a.index - b.index)
    data.maps.forEach((item, i) => {
      item.index = i + 1
      if (item.serverIsSort) {
        item.is_sort = true
        item.sort_type = item.sortType
        item.sort_index = item.sortIndex
      } else {
        item.is_sort = false
        item.sort_type = ''
        item.sort_index = 0
      }
    })
    SetTableConfig(data).then(() => {
      resolve(1)
    })
  })
}
// 获取列表配置
export const getTableConfig = (page_type, is_def) => {
  return GetTableConfig({ page_type, is_def: is_def || false }).then((res) => {
    res.data.maps.forEach((e) => {
      e.visible = e.is_show
    })
    return res.data
  })
}
// 列表宽度改变
export const tableWColumnWidthChange = (column, tableKey, type, $table) => {
  tableKey.forEach((x) => {
    if (x.key === column.field) {
      x.width = px22(column.renderWidth)
    } else if (!x.width) {
      x.width = px22($table.getTableColumn().fullColumn.find((y) => y.field === x.key).renderWidth)
    }
  })
  setTableConfig(tableKey, type, lineHeightType)
}
// 检查页面权限的通用函数
export const checkPagePermission = (pagePath: string) => {
  try {
    const userData = localStorage.getItem('userData')
    if (!userData) return false

    const userDataObj = JSON.parse(userData)
    const permissions = userDataObj.permissions_infos || []

    // 如果没有任何权限列表，直接返回false
    if (!permissions || permissions.length === 0) {
      return false
    }

    // 递归检查权限
    const hasPermission = (perms: any[], path: string): boolean => {
      return perms.some((perm) => {
        if (perm.path === path) return true
        if (perm.children && perm.children.length > 0) {
          return hasPermission(perm.children, path)
        }
        return false
      })
    }

    return hasPermission(permissions, pagePath)
  } catch (error) {
    console.error('检查页面权限失败:', error)
    return false
  }
}

// 页面内权限配置
export const checkedBtnPermission = async (code?: string) => {
  return new Promise((resolve) => {
    const path = code || useRoute().path
    const arr: Record<string, boolean> = {}

    try {
      // 从 userData 中获取权限信息
      const userDataStr = localStorage.getItem('userData')
      if (!userDataStr) {
        resolve(arr)
        return
      }

      const userData = JSON.parse(userDataStr)
      const permissionsInfos = userData.permissions_infos || []
      // 递归处理按钮权限列表（包括嵌套的children）
      const processBtnList = (btnList: any[]) => {
        btnList.forEach((btnItem: any) => {
          arr[`${btnItem.id}`] = true

          // 递归处理按钮的子权限
          if (btnItem.children && Array.isArray(btnItem.children)) {
            processBtnList(btnItem.children)
          }
        })
      }

      // 递归查找匹配路径的权限
      const findPermissionsByPath = (permissions: any[], targetPath: string) => {
        for (const permission of permissions) {
          // 检查当前权限项的路径
          if (permission.path === targetPath) {
            // 如果有 btnList，添加到权限数组中
            if (permission.btnList && Array.isArray(permission.btnList)) {
              processBtnList(permission.btnList)
            }
            return true
          }

          // 递归检查子权限
          if (permission.children && Array.isArray(permission.children)) {
            if (findPermissionsByPath(permission.children, targetPath)) {
              return true
            }
          }
        }
        return false
      }

      findPermissionsByPath(permissionsInfos, path)
    } catch (error) {
      console.error('获取权限信息失败:', error)
    }

    resolve(arr)
  })
}
export const initTable = (pageType, $table, tableKey, lineHeightType, isConfigDefault) => {
  return new Promise((resolve) => {
    getTableConfig(pageType, isConfigDefault)
      .then((res) => {
        lineHeightType.value = res.line_height
        lineHeightType = res.line_height
        if ($table) {
          tableKey.value = []
          setTimeout(() => {
            const defaultTableKey = pageTableConfig[pageType]

            tableKey.value = defaultTableKey
              .map((v) => {
                const val = { ...v }
                const serverObj = res?.maps?.find((x) => x.key === v.key)
                if (serverObj) {
                  Object.assign(val, {
                    index: serverObj.index,
                    width: serverObj.width,
                    is_show: serverObj.is_show,
                    freeze: serverObj.freeze,
                    serverIsSort: serverObj.is_sort,
                    sortType: serverObj.sort_type || 'ASC',
                    sortIndex: serverObj.sort_index,
                  })
                }
                return val
              })
              .sort((a, b) => a.index - b.index)

            setTimeout(() => {
              $table.getTableColumn().fullColumn.forEach((x) => {
                res.maps.forEach((y) => {
                  if (x.field === y.key) {
                    x.visible = y.is_show
                    x.width = x.width ? ((window.innerWidth * Number(x.width)) / 1920).toFixed(0) : null
                  }
                })
              })
              $table.refreshColumn()
              resolve(tableKey.value)
            }, 0)
          }, 0)
        }
      })
      .catch((e) => {
        console.log(e)

        if ($table) {
          tableKey.value = []
          setTimeout(() => {
            const defaultTableKey = pageTableConfig[pageType]
            tableKey.value = defaultTableKey
            setTimeout(() => {
              $table.getTableColumn().fullColumn.forEach((x) => {
                defaultTableKey.forEach((y) => {
                  if (x.field === y.key) {
                    x.visible = y.is_show
                    x.width = x.width ? ((window.innerWidth * Number(x.width)) / 1920).toFixed(0) : null
                  }
                })
              })
              $table.refreshColumn()
              resolve(true)
            }, 0)
          }, 0)
        }
        resolve(true)
      })
  })
}
export const validateStr = async (_rule, value, num) => {
  let limit = 200
  if (typeof num == 'number') limit = num
  if (String(value).length > limit) {
    return Promise.reject()
  }
  return Promise.resolve()
}

export const validateFile = async (_rule, value) => {
  console.log(value)

  if (value.length == 0) {
    console.log(1)

    return Promise.reject()
  }
  console.log(2)

  return Promise.resolve()
}

export const validateLink = async (_rule, value) => {
  if (!/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(value) && value != null && value != '') {
    return Promise.reject()
  }
  return Promise.resolve()
}

export const validateMail = async (_rule, value) => {
  if (!/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/.test(value) && value != '' && value != null) {
    return Promise.reject()
  }
  return Promise.resolve()
}
export const imgLoadErrorFallBack = () => {
  return 'data:image/png;base64,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'
}
export const getUrlParams = (name) => {
  const queryString = window.location.search || window.location.hash.split('?')[1]
  const urlParams = new URLSearchParams(queryString)
  return urlParams.get(name)
}
// 退出登录前的数据清理
export const beforLogout = () => {
  // 保留的localStorage字段（移除autoLogin，退出登录时应该清除自动登录状态）
  const keysToKeep = Object.keys(localStorage).filter((key) => ['userPsd', 'userAccount', 'rememberUserPsd', 'showAuditRecord'].indexOf(key) != -1)

  const obj = {}
  keysToKeep.forEach((key) => {
    const value = localStorage.getItem(key)
    obj[key] = value
  })

  // 清空localStorage
  localStorage.clear()

  // 恢复保留的字段
  for (const key in obj) {
    localStorage.setItem(key, obj[key])
  }

  // 清除水印
  eventBus.emit('clearWatermark')
}

// px 根据 rem缩放
export const px2 = (px) => {
  return +((window.innerWidth * Number(px)) / 1920).toFixed(0)
}

export const px22 = (px) => {
  return ((px * 1920) / window.innerWidth).toFixed(0)
}

// 将千分符的数字转换为数字 如果不是数字返回0
export const formatNum = (num) => {
  return `${num}`.replace(/,/g, '') || 0
}

/**
 * @description 根据value获取option的label
 * @param value 值
 * @param options 选项
 * @returns
 */
export const formatOptionLabel = (value, options) => {
  const option = options.find((item) => item.value == value)
  return option ? option.label : ''
}

/**
 * @description  数字格式化
 * @param {number} num 数字
 * @param {number} n 小数点后位数
 */
export const number2 = (num, n = 2) => {
  return num ? num.toFixed(n).replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0.00'
}

export const urlFormat = (url) => {
  url = url.startsWith('/') ? url : `/${url}`

  url = url.startsWith('http') ? url : `${import.meta.env.VITE_APP_BASE_API}${url}`

  return url
}

export const download = (res, fileName = 'table') => {
  const { data, headers } = res
  const blob = new Blob([data], { type: `${headers['content-type']};charset=utf-8` })
  const dom = document.createElement('a')
  const url = window.URL.createObjectURL(blob)
  dom.href = url
  dom.download = decodeURI(`${fileName}`)
  dom.style.display = 'none'
  document.body.appendChild(dom)
  dom.click()
  // dom.parentNode && dom.parentNode.removeChild(dom)
  if (dom.parentNode) {
    dom.parentNode.removeChild(dom)
  }
  window.URL.revokeObjectURL(url)
}

export const filterOption = (input, option) => {
  if (!input) return true
  if (!option || !option.label) return false
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

export const cloneDeep = (data: any) => JSON.parse(JSON.stringify(data))

export const getNowDateTime = (symbol = '-') => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')

  return {
    year,
    month,
    day,
    hours,
    minutes,
    seconds,
    formattedDate: `${year}${symbol}${month}${symbol}${day}`,
    formattedTime: `${hours}:${minutes}:${seconds}`,
    formattedDateTime: `${year}${symbol}${month}${symbol}${day} ${hours}:${minutes}:${seconds}`,
  }
}

// 确保返回值为字符串，若传入值为假值则返回空字符串
export const defEmptyStr = (val: any) => {
  return val || ''
}

/**
 * @description 获取公共选项
 * @param keys 选项keys
 * @returns 选项
 */
export const getCommonOption = async (keys: number[]) => {
  const commonOptionStore = useCommonOptionStore()
  const res = await commonOptionStore.getCommonOption(keys)
  return res
}

export const createId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
// 导出成功通知
export const exportSuccess = (fileId: number, url: string, fileName: string) => {
  // 这里可以显示一个成功通知，或者触发其他操作
  console.log('导出成功:', { fileId, url, fileName })
  // 可以根据需要添加通知逻辑
}

// 格式化价格
export const formatPrice = (price: any) => [null, ''].includes(price) ? '0' : price.toFixed(2)

/**
 * 检查用户是否具有指定权限
 * @param permissionId 权限ID
 * @param userPermissions 用户权限对象（可选，如果不传则从localStorage获取）
 * @returns 是否有权限
 */
export const checkPermission = (permissionId: string, userPermissions?: Record<string, boolean>): boolean => {
  try {
    // 如果传入了权限对象，直接使用
    if (userPermissions) {
      return userPermissions[permissionId] || false
    }

    // 从localStorage获取用户数据
    const userDataStr = localStorage.getItem('userData')
    if (!userDataStr) {
      return false
    }

    const userData = JSON.parse(userDataStr)
    const permissionsInfos = userData.permissions_infos || []

    // 递归查找权限
    const findPermission = (permissions: any[], targetId: string): boolean => {
      for (const permission of permissions) {
        // 检查当前权限项的按钮权限
        if (permission.btnList && Array.isArray(permission.btnList)) {
          const hasPermission = permission.btnList.some((btn: any) => {
            if (btn.id === targetId) return true
            // 递归检查按钮的子权限
            if (btn.children && Array.isArray(btn.children)) {
              return btn.children.some((child: any) => child.id === targetId)
            }
            return false
          })
          if (hasPermission) return true
        }

        // 递归检查子权限
        if (permission.children && Array.isArray(permission.children)) {
          if (findPermission(permission.children, targetId)) {
            return true
          }
        }
      }
      return false
    }

    return findPermission(permissionsInfos, permissionId)
  } catch (error) {
    console.error('检查权限失败:', error)
    return false
  }
}