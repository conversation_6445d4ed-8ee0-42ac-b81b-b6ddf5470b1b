// 组织架构相关类型定义

// 组织架构操作枚举
export enum ArchitectureOperation {
  ADD = 'add',        // 添加
  EDIT = 'edit',      // 编辑
  DELETE = 'delete',  // 删除
  VIEW = 'view',      // 查看
  UP = 'up',          // 上移
  DOWN = 'down',      // 下移
}

// 组织架构节点类型
export interface ArchitectureNode {
  id: string | number
  name: string
  full_name?: string
  type: number // 1=企业, 2=单位, 3=部门
  p_id?: string | number
  level?: number
  childs?: ArchitectureNode[]
  
  // 兼容性字段
  department_name?: string
  company_name?: string
}

// 组织架构操作参数
export interface ArchitectureOperationParams {
  operation: ArchitectureOperation
  node?: ArchitectureNode
  moveType?: number // 移动类型：1=上移, 2=下移
}

// 导出默认类型
export default ArchitectureOperation
