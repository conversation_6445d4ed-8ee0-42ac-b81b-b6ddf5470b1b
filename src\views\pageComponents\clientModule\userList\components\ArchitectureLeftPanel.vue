<template>
  <div :class="['architecture-tree mr-[16px] flex flex-col', showTree ? 'w-[230px]' : 'w-[0px]']">
    <a-tree-select
      v-show="showTree"
      v-model:value="selectBloc"
      :tree-data="blocOption"
      :tree-line="true && { showLeafIcon: false }"
      :fieldNames="{ label: 'department_name', value: 'id', children: 'childs' }"
      class="w-full"
      @change="handleChangeBloc"
      show-search
      placeholder="请输入企业名称"
      :filter-tree-node="(v: string, o: any) => filterOptionWithPinyin(v, o, 'department_name')"
    />
    <div class="b-#D9D9D9 b-solid b-1px mt-8px relative flex flex-1 flex-col">
      <div
        class="transform-translate-y-[-50%] z-2 text-16px h-40px w-14px b-#D9D9D9 b-solid b-1px bg-#fff hover:bg-#F8F8F9 absolute right-[-2px] top-[50%] flex cursor-pointer items-center"
        @click="handleChangeTreeStatus"
      >
        <LeftOutlined v-show="showTree" class="c-#999 text-11px pl-1px" />
        <RightOutlined v-show="!showTree" class="c-#999 text-11px pl-1px" />
      </div>

      <div class="bg-#FAFAFB p-12px border-r-4px box-border" v-show="showTree">
        <a-select
          class="w-full"
          v-model:value="filterText"
          placeholder="请输入"
          :options="headerOption"
          show-search
          :filter-option="(v: string, o: any) => filterOptionWithPinyin(v, o, 'department_name')"
          allowClear
          :field-names="{ label: 'account_name', value: 'id' }"
          @change="handleSelectTreeFilter"
        >
          <template #option="{ department_name, company_name }">
            <div class="select-option" :title="department_name">
              <div class="option-name truncate">{{ department_name }}</div>
              <div class="option-info text-#999 truncate">
                {{ company_name.replaceAll('/', '>') }}
              </div>
            </div>
          </template>
        </a-select>
      </div>
      <div class="p-12px scroll-bar relative box-border flex-1 overflow-y-auto overflow-x-hidden" v-show="showTree">
        <a-tree
          class="absolute w-full"
          node-key="id"
          :fieldNames="{
            children: 'childs',
            title: 'department_name',
            key: 'id',
          }"
          defaultExpandAll
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="deptTree"
          @select="handleSelectDept"
        >
          <template #title="data">
            <div class="flex w-full items-center justify-between" :id="data.id">
              <a-tooltip :title="data.department_name" v-if="isEllipsis(data.department_name)" placement="topLeft">
                <span class="truncate" :style="{ width: `${120 - (data.level + 1) * 8}px` }">{{ data.department_name }}</span>
              </a-tooltip>
              <span v-else class="w-120px truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.department_name }}</span>
              <a-space class="action-icons">
                <a-tooltip placement="bottom" v-if="checkPermission(per.arch_add)">
                  <template #title>
                    <span>添加子部啊门</span>
                  </template>
                  <PlusOutlined class="text-10px c-#999 mt-8px" @click.stop="handleAddSonDept(data)" />
                </a-tooltip>
                <a-dropdown v-if="![1, 2].includes(data.type)">
                  <MoreOutlined class="c-#999 mt-8px" @click.stop />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-for="item in filterOperationList" :key="item.key" @click.stop="item.onClick(data)">
                        <div class="gap-8px flex items-center" :class="item.class">
                          <i class="iconfont" :class="item.icon"></i>
                          <span>{{ item.label }}</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </div>
          </template>
        </a-tree>
      </div>
      <div class="b-#D9D9D9 b-t-solid b-t-[1px] flex" v-show="showTree">
        <div class="p-8px flex flex-1 cursor-pointer items-center justify-center" @click="toggleExpandAll">
          <DoubleRightOutlined :class="['expand-icon', { expanded: isAllExpanded }]" />
          <span>{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
        </div>
        <div class="p-8px bg-#409EFF flex flex-1 cursor-pointer items-center justify-center text-white" @click="handleAddDept" v-permission="per.arch_add">
          <PlusCircleOutlined class="mr-4px" />
          <span>新建部门</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { per } from '@/common/permission'
import { checkPermission } from '@/utils/index'
import { PlusOutlined, MoreOutlined, DoubleRightOutlined, PlusCircleOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { ArchitectureOperation } from '@/types/features/architecture.type'
import { setLocalStorage } from '@/utils/catch'

const selectArchitecture = defineModel<any>('selectArchitecture', { required: true })
const selectBloc = inject<any>('selectBloc', null)
const blocOption = inject<any>('blocOption', [])
const spinning = inject<any>('spinning', ref(false))

const emits = defineEmits<{
  (e: 'changeBloc'): void
  (e: 'resetPageQuery'): void
  (e: 'operation', operation: ArchitectureOperation, row?: any, moveType?: number): void
}>()

const props = defineProps<{
  architectureTree: any[]
}>()

const showTree = defineModel('showTree')

// 部门树
const deptTree = defineModel<any[]>('deptTree', { required: true })
// 过滤搜索
const filterText = ref<null | string>(null)
const headerOption = ref<any[]>([])
// 是否展开全部
const isAllExpanded = ref(true)
// 架构树选中节点
const selectedKeys = ref<string[]>([])
// 架构树展开节点
const expandedKeys = ref<string[]>([])

const operationList = [
  {
    label: '查看详情',
    key: '1',
    icon: 'icon-chakanxiangqing',
    onClick: (d: any) => emits('operation', ArchitectureOperation.VIEW, d),
  },
  {
    label: '编辑信息',
    key: '2',
    icon: 'icon-bianjixinxi',
    permission: per.arch_edit,
    onClick: (d: any) => emits('operation', ArchitectureOperation.EDIT, d),
  },
  {
    label: '上移',
    key: '3',
    icon: 'icon-shangyi',
    permission: per.arch_updown,
    onClick: (d: any) => emits('operation', ArchitectureOperation.UP, d, 1),
  },
  {
    label: '下移',
    key: '4',
    icon: 'icon-xiayi',
    permission: per.arch_updown,
    onClick: (d: any) => emits('operation', ArchitectureOperation.DOWN, d, 1),
  },
  {
    label: '删除',
    key: '5',
    icon: 'icon-shanchu',
    permission: per.arch_delete,
    onClick: (d: any) => emits('operation', ArchitectureOperation.DELETE, d),
    class: 'text-red-500',
  },
]
// 过滤无权限
const filterOperationList = computed(() => operationList.filter((item) => !item.permission || checkPermission(item.permission)))

//树形架构添加显示样式
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是你设置的最大宽度
  return width > 120
}

const handleSelectDept = (selectedKeys: string[]) => {
  if (!selectedKeys.length) return
  selectArchitecture.value = selectedKeys[0]
  emits('resetPageQuery')
}

const handleAddSonDept = (data: any) => {
  emits('operation', ArchitectureOperation.ADD, data)
}

const handleAddDept = () => {
  emits('operation', ArchitectureOperation.ADD)
}

const handleChangeBloc = () => {
  emits('changeBloc')
}

const toggleExpandAll = () => {
  spinning.value = true
  setTimeout(() => {
    if (isAllExpanded.value) {
      // 收起所有节点
      expandedKeys.value = []
    } else {
      // 展开所有节点
      const getAllKeys = (data: any[]): string[] => {
        let keys: string[] = []
        data.forEach((item) => {
          keys.push(item.id)
          if (item.childs && item.childs.length > 0) {
            keys = keys.concat(getAllKeys(item.childs))
          }
        })
        return keys
      }
      expandedKeys.value = getAllKeys(deptTree.value)
    }
    isAllExpanded.value = !isAllExpanded.value
    nextTick(() => {
      spinning.value = false
    })
  }, 200)
}

// 选择部门树
const handleSelectTreeFilter = (newVal: string) => {
  console.log(newVal)
  if (newVal) {
    filterText.value = null
    selectedKeys.value = [newVal]
    selectArchitecture.value = newVal
    // 找到选中节点的所有父节点ID
    const findParentIds = (tree: any[], targetId: string, parentIds: string[] = []): string[] => {
      for (const node of tree) {
        if (node.id === targetId) return parentIds
        if (node.childs && node.childs.length) {
          const found = findParentIds(node.childs, targetId, [...parentIds, node.id])
          if (found.length) return found
        }
      }
      return []
    }
    // 获取所有需要展开的节点ID
    const parentIds = findParentIds(deptTree.value, newVal)
    expandedKeys.value = [...new Set([...expandedKeys.value, ...parentIds, newVal])]
    nextTick(() => {
      emits('resetPageQuery')
      const node = document.getElementById(newVal)
      if (node) {
        // 兼容性更好的滚动实现
        scrollToElement(node)
      }
    })
  }
}

// 兼容性更好的滚动方法
const scrollToElement = (element: HTMLElement) => {
  try {
    // 1. 优先尝试现代浏览器的scrollIntoView
    if (typeof element.scrollIntoView === 'function') {
      try {
        element.scrollIntoView({ block: 'center' }) // 移除动画参数
        return
      } catch (e) {
        console.log(e)
        // 降级到基础版本
        element.scrollIntoView()
        return
      }
    }

    // 2. 直接计算并设置滚动位置（无动画）
    let container: HTMLElement | null = element.parentElement

    // 智能查找滚动容器
    while (container && container !== document.documentElement) {
      const style = getComputedStyle(container)
      if (
        container.classList.contains('scroll-bar') ||
        style.overflowY === 'auto' ||
        style.overflowY === 'scroll' ||
        container.tagName === 'BODY' // 包括body
      ) {
        break
      }
      container = container.parentElement
    }

    // 回退到documentElement
    if (!container || container === document.body) {
      container = document.documentElement
    }

    // 3. 精确计算滚动位置
    const elementRect = element.getBoundingClientRect()
    const containerRect = container.getBoundingClientRect()

    // 计算元素在容器中的相对位置
    const elementTopInContainer = elementRect.top - containerRect.top

    // 计算目标位置：元素顶部到容器顶部 + 当前滚动位置 - 容器高度的一半
    const targetPosition = container.scrollTop + elementTopInContainer - container.clientHeight / 2 + elementRect.height / 2

    // 4. 边界检查
    const maxScroll = container.scrollHeight - container.clientHeight
    const finalPosition = Math.max(0, Math.min(targetPosition, maxScroll))

    // 5. 直接设置滚动位置
    container.scrollTop = finalPosition
  } catch (error) {
    console.warn('滚动到元素失败:', error)
    // 最终降级方案
    if (element) {
      element.scrollIntoView()
    }
  }
}

const findHeaderOption = (id: string) => {
  const findNode = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.childs && node.childs.length) {
        const found = findNode(node.childs)
        if (found) return found
      }
      return null
    }
  }

  const flattenNode = (node: any): any[] => {
    const result: any[] = []
    if (node) {
      if (node.type !== 2 || (node.type === 2 && node.status !== 0)) {
        result.push({
          id: node.id,
          department_name: node.department_name,
          company_name: node.company_name || '',
          type: node.type,
        })
        if (node.childs && node.childs.length) {
          node.childs.forEach((child: any) => {
            result.push(...flattenNode(child))
          })
        }
      }
    }
    return result
  }

  const foundNode = findNode(props.architectureTree)
  if (foundNode) {
    headerOption.value = flattenNode(foundNode).filter((item) => ![1].includes(item.type))
  }
}

// 展开两层节点
const handleExpandTowLevel = () => {
  const tree = deptTree.value
  const expanded: string[] = []
  const firstNode = tree?.[0]
  if (firstNode) {
    expanded.push(firstNode.id)
    selectedKeys.value = [firstNode.id]
  } else {
    selectedKeys.value = []
  }
  expandedKeys.value = expanded
}

const setSelectKeys = () => {
  const id = deptTree.value[0].id
  selectedKeys.value = [id]
  selectArchitecture.value = id
}

const handleChangeTreeStatus = () => {
  showTree.value = !showTree.value
  setLocalStorage('showTree', showTree.value === false ? 'true' : 'false')
}

defineExpose({
  handleExpandTowLevel,
  findHeaderOption,
  setSelectKeys,
})
</script>

<style scoped lang="scss">
.architecture-tree {
  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }
  }

  :deep(.ant-tree-switcher) {
    line-height: 32px;
  }

  :deep(.ant-tree-treenode-selected) {
    color: #409eff;
    background-color: #eef2fa;
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa;
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }

  // 使用 SCSS 循环生成15级缩进样式
  @for $i from 1 through 15 {
    :deep(.level-#{$i}) {
      .ant-tree-indent {
        width: $i * 8px;
      }
    }
  }
}

.expand-icon {
  margin-right: 4px;
  transform: rotate(90deg);

  &.expanded {
    transform: rotate(-90deg);
  }
}
</style>
