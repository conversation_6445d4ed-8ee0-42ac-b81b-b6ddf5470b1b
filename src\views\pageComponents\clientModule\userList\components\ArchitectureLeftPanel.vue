<template>
  <div :class="['architecture-tree mr-[16px] flex flex-col', showTree ? 'w-[230px]' : 'w-[0px]']">
    <a-tree-select
      v-show="showTree"
      v-model:value="selectBloc"
      :tree-data="blocOption"
      :tree-line="true && { showLeafIcon: false }"
      :fieldNames="{ label: 'name', value: 'id', children: 'childs' }"
      class="w-full"
      @change="handleChangeBloc"
      show-search
      placeholder="请输入企业名称"
      :filter-tree-node="(v: string, o: any) => filterOptionWithPinyin(v, o, 'name')"
    />
    <div class="b-#D9D9D9 b-solid b-1px mt-8px relative flex flex-1 flex-col">
      <div
        class="transform-translate-y-[-50%] z-2 text-16px h-40px w-14px b-#D9D9D9 b-solid b-1px bg-#fff hover:bg-#F8F8F9 absolute right-[-2px] top-[50%] flex cursor-pointer items-center"
        @click="handleChangeTreeStatus"
      >
        <LeftOutlined v-show="showTree" class="c-#999 text-11px pl-1px" />
        <RightOutlined v-show="!showTree" class="c-#999 text-11px pl-1px" />
      </div>

      <div class="bg-#FAFAFB p-12px border-r-4px box-border" v-show="showTree">
        <a-select
          class="w-full"
          v-model:value="filterText"
          placeholder="请输入"
          :options="headerOption"
          show-search
          :filter-option="(v: string, o: any) => filterOptionWithPinyin(v, o, 'name')"
          allowClear
          :field-names="{ label: 'name', value: 'id' }"
          @change="handleSelectTreeFilter"
        >
          <template #option="{ name, full_name }">
            <div class="select-option" :title="name">
              <div class="option-name truncate">{{ name }}</div>
              <div class="option-info text-#999 truncate">
                {{ full_name?.replaceAll('/', '>') }}
              </div>
            </div>
          </template>
        </a-select>
      </div>
      <div class="p-12px scroll-bar relative box-border flex-1 overflow-y-auto overflow-x-hidden" v-show="showTree">
        <a-tree
          class="absolute w-full"
          node-key="id"
          :fieldNames="{
            children: 'childs',
            title: 'name',
            key: 'id',
          }"
          defaultExpandAll
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="deptTree"
          @select="handleSelectDept"
        >
          <template #title="data">
            <div class="flex w-full items-center justify-between" :id="data.id">
              <a-tooltip :title="data.name" v-if="isEllipsis(data.name)" placement="topLeft">
                <span class="truncate" :style="{ width: `${120 - (data.level + 1) * 8}px` }">{{ data.name }}</span>
              </a-tooltip>
              <span v-else class="w-120px truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
              <a-space class="action-icons">
                <a-tooltip placement="bottom" v-if="checkPermission(per.arch_add)">
                  <template #title>
                    <span>添加子部啊门</span>
                  </template>
                  <PlusOutlined class="text-10px c-#999 mt-8px" @click.stop="handleAddSonDept(data)" />
                </a-tooltip>
                <a-dropdown v-if="![1, 2].includes(data.type)">
                  <MoreOutlined class="c-#999 mt-8px" @click.stop />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-for="item in filterOperationList" :key="item.key" @click.stop="item.onClick(data)">
                        <div class="gap-8px flex items-center" :class="item.class">
                          <i class="iconfont" :class="item.icon"></i>
                          <span>{{ item.label }}</span>
                        </div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </div>
          </template>
        </a-tree>
      </div>
      <div class="b-#D9D9D9 b-t-solid b-t-[1px] flex" v-show="showTree">
        <div class="p-8px flex flex-1 cursor-pointer items-center justify-center" @click="toggleExpandAll">
          <DoubleRightOutlined :class="['expand-icon', { expanded: isAllExpanded }]" />
          <span>{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
        </div>
        <div class="p-8px bg-#409EFF flex flex-1 cursor-pointer items-center justify-center text-white" @click="handleAddDept" v-permission="per.arch_add">
          <PlusCircleOutlined class="mr-4px" />
          <span>新建部门</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { per } from '@/common/permission'
import { checkPermission } from '@/utils/index'
import { PlusOutlined, MoreOutlined, DoubleRightOutlined, PlusCircleOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { ArchitectureOperation } from '@/types/features/architecture.type'
import { setLocalStorage } from '@/utils/catch'
import { GetCompanyTreeAuto, DeleteDepartment, DepartmentMove, AddDepartment, type TreeNode, type SearchOption } from '@/servers/CompanyArchitecture'
import { message } from 'ant-design-vue'
import { onMounted, watch, nextTick } from 'vue'
import { filterOptionWithPinyin } from '@/utils/pinyin'

const selectArchitecture = defineModel<any>('selectArchitecture', { required: true })
const selectBloc = inject<any>('selectBloc', null)
const blocOption = inject<any>('blocOption', [])
const spinning = inject<any>('spinning', ref(false))

const emits = defineEmits<{
  (e: 'changeBloc'): void
  (e: 'resetPageQuery'): void
  (e: 'operation', operation: ArchitectureOperation, row?: any, moveType?: number): void
}>()

const props = defineProps<{
  architectureTree: any[]
}>()

const showTree = defineModel('showTree')

// 部门树
const deptTree = defineModel<any[]>('deptTree', { required: true })
// 过滤搜索
const filterText = ref<null | string>(null)
const headerOption = ref<any[]>([])
// 是否展开全部
const isAllExpanded = ref(true)
// 架构树选中节点
const selectedKeys = ref<string[]>([])
// 架构树展开节点
const expandedKeys = ref<string[]>([])

// 添加数据获取相关的响应式变量
const loading = ref(false)
const allKeysCache = ref<string[]>([])

// 获取组织架构数据
const getAllDept = async (type?: string) => {
  try {
    loading.value = true
    const res = await GetCompanyTreeAuto()
    const data = res.data || []

    // 数据标准化处理，确保兼容性
    const normalizeTreeData = (nodes: TreeNode[]): any[] => {
      return nodes.map((node) => ({
        ...node,
        // 保持向后兼容，同时支持新字段
        department_name: node.name,
        company_name: node.full_name,
        // 确保 childs 字段存在
        childs: node.childs ? normalizeTreeData(node.childs) : [],
      }))
    }

    const normalizedData = normalizeTreeData(data)
    deptTree.value = normalizedData

    // 缓存所有节点的keys用于展开/收起功能
    const getAllKeys = (nodes: any[]): string[] => {
      let keys: string[] = []
      nodes.forEach((node) => {
        keys.push(String(node.id))
        if (node.childs && node.childs.length > 0) {
          keys = keys.concat(getAllKeys(node.childs))
        }
      })
      return keys
    }
    allKeysCache.value = getAllKeys(normalizedData)

    // 初始化时展开前两层
    if (type === 'init') {
      handleExpandTowLevel()
    }

    // 更新搜索选项
    findHeaderOption()
  } catch (error: any) {
    console.error('获取组织架构数据失败:', error)
    message.error(error?.message || '获取组织架构数据失败')
  } finally {
    loading.value = false
  }
}

const operationList = [
  {
    label: '查看详情',
    key: '1',
    icon: 'icon-chakanxiangqing',
    onClick: (d: any) => emits('operation', ArchitectureOperation.VIEW, d),
  },
  {
    label: '编辑信息',
    key: '2',
    icon: 'icon-bianjixinxi',
    permission: per.arch_edit,
    onClick: (d: any) => emits('operation', ArchitectureOperation.EDIT, d),
  },
  {
    label: '上移',
    key: '3',
    icon: 'icon-shangyi',
    permission: per.arch_updown,
    onClick: (d: any) => emits('operation', ArchitectureOperation.UP, d, 1),
  },
  {
    label: '下移',
    key: '4',
    icon: 'icon-xiayi',
    permission: per.arch_updown,
    onClick: (d: any) => emits('operation', ArchitectureOperation.DOWN, d, 1),
  },
  {
    label: '删除',
    key: '5',
    icon: 'icon-shanchu',
    permission: per.arch_delete,
    onClick: (d: any) => handleDeleteDept(d),
    class: 'text-red-500',
  },
]
// 过滤无权限
const filterOperationList = computed(() => operationList.filter((item) => !item.permission || checkPermission(item.permission)))

// 树形架构添加显示样式
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是你设置的最大宽度
  return width > 120
}

const handleSelectDept = (selectedKeys: string[]) => {
  if (!selectedKeys.length) return
  selectArchitecture.value = selectedKeys[0]
  emits('resetPageQuery')
}

const handleAddSonDept = (data: any) => {
  emits('operation', ArchitectureOperation.ADD, data)
}

const handleAddDept = () => {
  emits('operation', ArchitectureOperation.ADD)
}

const handleChangeBloc = () => {
  emits('changeBloc')
}

// 删除部门处理函数
const handleDeleteDept = async (data: any) => {
  try {
    // 使用 Modal.confirm 进行二次确认
    const { Modal } = await import('ant-design-vue')
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除部门？删除前，请先移除部门内所有人员再进行操作，否则会操作失败。',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          spinning.value = true
          await DeleteDepartment({
            id: String(data.id),
            p_id: String(data.p_id || ''),
            company_id: String(selectBloc?.value || ''),
            name: data.name || '',
            header_ids: 0,
            oa_id: String(data.oa_id || ''),
          })
          message.success('删除成功')
          await getAllDept() // 刷新数据
          emits('resetPageQuery')
        } catch (error: any) {
          message.error(error?.message || '删除失败')
        } finally {
          spinning.value = false
        }
      },
    })
  } catch (error: any) {
    console.error('删除操作失败:', error)
    message.error('删除操作失败')
  }
}

const toggleExpandAll = () => {
  if (isAllExpanded.value) {
    // 收起所有节点
    expandedKeys.value = []
  } else {
    // 展开所有节点，使用缓存的keys
    expandedKeys.value = [...allKeysCache.value]
  }
  isAllExpanded.value = !isAllExpanded.value
}

// 选择部门树
const handleSelectTreeFilter = (newVal: string) => {
  console.log(newVal)
  if (newVal) {
    filterText.value = null
    selectedKeys.value = [newVal]
    selectArchitecture.value = newVal
    // 找到选中节点的所有父节点ID
    const findParentIds = (tree: any[], targetId: string, parentIds: string[] = []): string[] => {
      for (const node of tree) {
        if (node.id === targetId) return parentIds
        if (node.childs && node.childs.length) {
          const found = findParentIds(node.childs, targetId, [...parentIds, node.id])
          if (found.length) return found
        }
      }
      return []
    }
    // 获取所有需要展开的节点ID
    const parentIds = findParentIds(deptTree.value, newVal)
    expandedKeys.value = [...new Set([...expandedKeys.value, ...parentIds, newVal])]
    nextTick(() => {
      emits('resetPageQuery')
      const node = document.getElementById(newVal)
      if (node) {
        // 兼容性更好的滚动实现
        scrollToElement(node)
      }
    })
  }
}

// 兼容性更好的滚动方法
const scrollToElement = (element: HTMLElement) => {
  try {
    // 1. 优先尝试现代浏览器的scrollIntoView
    if (typeof element.scrollIntoView === 'function') {
      try {
        element.scrollIntoView({ block: 'center' }) // 移除动画参数
        return
      } catch (e) {
        console.log(e)
        // 降级到基础版本
        element.scrollIntoView()
        return
      }
    }

    // 2. 直接计算并设置滚动位置（无动画）
    let container: HTMLElement | null = element.parentElement

    // 智能查找滚动容器
    while (container && container !== document.documentElement) {
      const style = getComputedStyle(container)
      if (
        container.classList.contains('scroll-bar') ||
        style.overflowY === 'auto' ||
        style.overflowY === 'scroll' ||
        container.tagName === 'BODY' // 包括body
      ) {
        break
      }
      container = container.parentElement
    }

    // 回退到documentElement
    if (!container || container === document.body) {
      container = document.documentElement
    }

    // 3. 精确计算滚动位置
    const elementRect = element.getBoundingClientRect()
    const containerRect = container.getBoundingClientRect()

    // 计算元素在容器中的相对位置
    const elementTopInContainer = elementRect.top - containerRect.top

    // 计算目标位置：元素顶部到容器顶部 + 当前滚动位置 - 容器高度的一半
    const targetPosition = container.scrollTop + elementTopInContainer - container.clientHeight / 2 + elementRect.height / 2

    // 4. 边界检查
    const maxScroll = container.scrollHeight - container.clientHeight
    const finalPosition = Math.max(0, Math.min(targetPosition, maxScroll))

    // 5. 直接设置滚动位置
    container.scrollTop = finalPosition
  } catch (error) {
    console.warn('滚动到元素失败:', error)
    // 最终降级方案
    if (element) {
      element.scrollIntoView()
    }
  }
}

const findHeaderOption = () => {
  const flattenNode = (nodes: any[]): any[] => {
    const result: any[] = []
    nodes.forEach((node: any) => {
      // 只包含非企业类型的节点（type !== 1）
      if (node.type !== 1) {
        result.push({
          id: node.id,
          name: node.name,
          full_name: node.full_name || '',
          type: node.type,
          // 保持向后兼容
          department_name: node.name,
          company_name: node.full_name || '',
        })
      }
      if (node.childs && node.childs.length) {
        result.push(...flattenNode(node.childs))
      }
    })
    return result
  }

  // 使用当前的部门树数据
  headerOption.value = flattenNode(deptTree.value)
}

// 展开两层节点
const handleExpandTowLevel = () => {
  const tree = deptTree.value
  const expanded: string[] = []
  const firstNode = tree?.[0]
  if (firstNode) {
    expanded.push(firstNode.id)
    selectedKeys.value = [firstNode.id]
  } else {
    selectedKeys.value = []
  }
  expandedKeys.value = expanded
}

const setSelectKeys = () => {
  const id = deptTree.value[0].id
  selectedKeys.value = [id]
  selectArchitecture.value = id
}

const handleChangeTreeStatus = () => {
  showTree.value = !showTree.value
  setLocalStorage('showTree', showTree.value === false ? 'true' : 'false')
}

// 组件挂载时初始化数据
onMounted(() => {
  getAllDept('init')
})

// 监听企业选择变化
watch(
  () => selectBloc?.value,
  (newVal) => {
    if (newVal) {
      getAllDept()
    }
  },
  { immediate: false },
)

defineExpose({
  handleExpandTowLevel,
  findHeaderOption,
  setSelectKeys,
  getAllDept, // 暴露刷新数据的方法
})
</script>

<style scoped lang="scss">
.architecture-tree {
  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }
  }

  :deep(.ant-tree-switcher) {
    line-height: 32px;
  }

  :deep(.ant-tree-treenode-selected) {
    color: #409eff;
    background-color: #eef2fa;
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa;
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    opacity: 0;
    transition: opacity 0.2s;
  }

  // 使用 SCSS 循环生成15级缩进样式
  @for $i from 1 through 15 {
    :deep(.level-#{$i}) {
      .ant-tree-indent {
        width: $i * 8px;
      }
    }
  }
}

.expand-icon {
  margin-right: 4px;
  transform: rotate(90deg);

  &.expanded {
    transform: rotate(-90deg);
  }
}
</style>
