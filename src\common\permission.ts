// 权限常量定义
// 基于用户管理页面的权限映射

export const per = {
  // 组织架构相关权限
  arch_add: '211004',      // 新建部门权限
  arch_edit: '211005',     // 编辑部门权限
  arch_delete: '211005',   // 删除部门权限（使用编辑权限）
  arch_view: '211006',     // 查看部门权限
  arch_updown: '211005',   // 上移下移权限（使用编辑权限）
  
  // 其他可能的权限常量可以在这里添加
  // 例如：
  // user_add: '211001',
  // user_edit: '211002',
  // user_delete: '211003',
} as const

// 权限类型定义
export type PermissionKey = keyof typeof per
export type PermissionValue = typeof per[PermissionKey]

// 权限检查辅助函数
export const hasPermission = (permission: PermissionKey, userPermissions: Record<string, boolean>): boolean => {
  const permissionId = per[permission]
  return userPermissions[permissionId] || false
}

// 导出默认权限对象
export default per
